// 模拟API服务，提供课程和句子数据

// 定义数据类型
export interface Course {
  id: string;
  title: string;
  description: string;
  sentences: Sentence[];
}

export interface Sentence {
  id: string;
  english: string;
  chinese: string;
  level: string;
}

// 课程数据
const mockCourses: { [key: string]: Course[] } = {
  'primary-1': [
    {
      id: '1',
      title: '基础日常用语',
      description: '学习小学一年级基础日常英语句子',
      sentences: [
        { id: '1-1-1', english: 'Hello!', chinese: '你好！', level: 'primary-1' },
        { id: '1-1-2', english: 'Good morning.', chinese: '早上好。', level: 'primary-1' },
        { id: '1-1-3', english: 'Good morning, Miss <PERSON>.', chinese: '早上好，怀特小姐。', level: 'primary-1' },
        { id: '1-1-4', english: 'Good morning, boys and girls.', chinese: '早上好，孩子们。', level: 'primary-1' },
        { id: '1-1-5', english: 'Thank you.', chinese: '谢谢。', level: 'primary-1' },
        { id: '1-1-6', english: 'What\'s your name?', chinese: '你叫什么名字?', level: 'primary-1' },
        { id: '1-1-7', english: 'I\'m Bill.', chinese: '我是Bill。', level: 'primary-1' },
        { id: '1-1-8', english: 'My name is Peter.', chinese: '我是彼得。', level: 'primary-1' },
        { id: '1-1-9', english: 'Good night.', chinese: '晚安!', level: 'primary-1' },
        { id: '1-1-10', english: 'I love you.', chinese: '我爱你。', level: 'primary-1' },
        { id: '1-1-11', english: 'Stand up, please.', chinese: '请起立。', level: 'primary-1' },
        { id: '1-1-12', english: 'Sit down, please.', chinese: '请坐下。', level: 'primary-1' },
        { id: '1-1-13', english: 'Show me your eraser.', chinese: '请出示你的橡皮。', level: 'primary-1' },
        { id: '1-1-14', english: 'How are you today?', chinese: '你今天好吗？', level: 'primary-1' },
        { id: '1-1-15', english: 'Thank you for asking, I\'m OK.', chinese: '谢谢你的问候，我很好。', level: 'primary-1' },
        
      ]
    },
    {
      id: '2',
      title: '数字和颜色',
      description: '学习数字1-10和基础颜色单词',
      sentences: [
        { id: '1-2-1', english: 'I have one book.', chinese: '我有一本书。', level: 'primary-1' },
        { id: '1-2-2', english: 'This is red.', chinese: '这是红色的。', level: 'primary-1' },
        { id: '1-2-3', english: 'I see three apples.', chinese: '我看到三个苹果。', level: 'primary-1' },
        { id: '1-2-4', english: 'The sky is blue.', chinese: '天空是蓝色的。', level: 'primary-1' },
        { id: '1-2-5', english: 'Five little ducks.', chinese: '五只小鸭子。', level: 'primary-1' }
      ]
    }
  ],
  'primary-2': [
    {
      id: '3',
      title: '学校生活',
      description: '学习与学校生活相关的英语表达',
      sentences: [
        { id: '2-3-1', english: 'I go to school every day.', chinese: '我每天去上学。', level: 'primary-2' },
        { id: '2-3-2', english: 'I like my teacher.', chinese: '我喜欢我的老师。', level: 'primary-2' },
        { id: '2-3-3', english: 'We have English class today.', chinese: '我们今天有英语课。', level: 'primary-2' },
        { id: '2-3-4', english: 'I have many friends at school.', chinese: '我在学校有很多朋友。', level: 'primary-2' },
        { id: '2-3-5', english: 'Let\'s play together.', chinese: '让我们一起玩。', level: 'primary-2' }
      ]
    },
    {
      id: '4',
      title: '家庭和朋友',
      description: '学习关于家庭和朋友的英语表达',
      sentences: [
        { id: '2-4-1', english: 'This is my father.', chinese: '这是我的爸爸。', level: 'primary-2' },
        { id: '2-4-2', english: 'My mother is a doctor.', chinese: '我的妈妈是一名医生。', level: 'primary-2' },
        { id: '2-4-3', english: 'I have a little brother.', chinese: '我有一个小弟弟。', level: 'primary-2' },
        { id: '2-4-4', english: 'Tom is my best friend.', chinese: '汤姆是我最好的朋友。', level: 'primary-2' },
        { id: '2-4-5', english: 'We are a happy family.', chinese: '我们是一个幸福的家庭。', level: 'primary-2' }
      ]
    }
  ],
  'junior-1': [
    {
      id: '5',
      title: '日常生活对话',
      description: '学习初中一年级日常英语对话',
      sentences: [
        { id: '3-5-1', english: 'What time is it now?', chinese: '现在几点了？', level: 'junior-1' },
        { id: '3-5-2', english: 'I usually get up at 7 o\'clock.', chinese: '我通常7点起床。', level: 'junior-1' },
        { id: '3-5-3', english: 'Do you like playing basketball?', chinese: '你喜欢打篮球吗？', level: 'junior-1' },
        { id: '3-5-4', english: 'My favorite subject is English.', chinese: '我最喜欢的科目是英语。', level: 'junior-1' },
        { id: '3-5-5', english: 'How was your weekend?', chinese: '你周末过得怎么样？', level: 'junior-1' }
      ]
    }
  ],
  'senior-1': [
    {
      id: '6',
      title: '高中英语基础',
      description: '高中一年级英语基础句子',
      sentences: [
        { id: '4-6-1', english: 'It is important to learn English well.', chinese: '学好英语很重要。', level: 'senior-1' },
        { id: '4-6-2', english: 'I dream of becoming a doctor in the future.', chinese: '我梦想将来成为一名医生。', level: 'senior-1' },
        { id: '4-6-3', english: 'Reading books can broaden our horizons.', chinese: '读书可以开阔我们的视野。', level: 'senior-1' },
        { id: '4-6-4', english: 'We should protect the environment.', chinese: '我们应该保护环境。', level: 'senior-1' },
        { id: '4-6-5', english: 'Practice makes perfect.', chinese: '熟能生巧。', level: 'senior-1' }
      ]
    }
  ]
};

// 获取课程列表
export const getCourses = (level: string): Promise<Course[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockCourses[level] || []);
    }, 300);
  });
};

// 获取课程详情
export const getCourseDetail = (level: string, id: string): Promise<Course | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const courses = mockCourses[level] || [];
      const course = courses.find(c => c.id === id) || null;
      resolve(course);
    }, 300);
  });
};

// 获取句子详情
export const getSentenceDetail = (sentenceId: string): Promise<Sentence | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let foundSentence: Sentence | null = null;
      
      Object.values(mockCourses).forEach(levelCourses => {
        levelCourses.forEach(course => {
          const sentence = course.sentences.find(s => s.id === sentenceId);
          if (sentence) {
            foundSentence = sentence;
          }
        });
      });
      
      resolve(foundSentence);
    }, 300);
  });
};