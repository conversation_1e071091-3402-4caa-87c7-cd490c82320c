{"name": "learn-english-for-kids", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@types/vue-router": "^2.0.0", "axios": "^1.12.2", "element-plus": "^2.11.4", "vue": "^3.5.21", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.8.1", "typescript": "~5.8.3", "vite": "^7.1.7", "vue-tsc": "^3.0.7"}}