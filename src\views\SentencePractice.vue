<template>
  <div class="sentence-practice-container">
    <el-header class="header">
      <div class="header-content">
        <el-button @click="goBack" icon="el-icon-arrow-left" size="large"></el-button>
        <h1>句子练习</h1>
      </div>
    </el-header>
    
    <el-main class="main-content">
      <div v-if="loading">
        <el-loading-spinner type="spinner"></el-loading-spinner>
        <span>加载中...</span>
      </div>
      
      <div v-else-if="sentence">
        <div class="practice-card">
          <!-- 进度条 -->
          <div class="progress-container">
            <el-progress 
              :percentage="progressPercentage" 
              :status="progressStatus"
              :stroke-width="8"
              text-inside
            />
          </div>
          
          <!-- 句子卡片 -->
          <el-card class="sentence-display-card">
            <div class="sentence-avatar">
              <div v-if="currentAvatarIndex === 0" class="avatar-icon">
                <i class="el-icon-user"></i>
              </div>
              <div v-else-if="currentAvatarIndex === 1" class="avatar-icon">
                <i class="el-icon-girl"></i>
              </div>
              <div v-else class="avatar-icon">
                <i class="el-icon-boy"></i>
              </div>
            </div>
            
            <div class="sentence-display">
              <div class="english-sentence">
                {{ sentence.english }}
              </div>
              
              <div class="sentence-audio">
                <el-button 
                  @click="playAudio" 
                  type="primary" 
                  icon="el-icon-video-play" 
                  circle
                ></el-button>
                <span>播放发音</span>
              </div>
            </div>
          </el-card>
          
          <!-- 语音设置控件 -->
          <div class="voice-settings">
            <div class="speed-control">
              <span>语速: {{ speedRate.toFixed(1) }}x</span>
              <el-slider 
                v-model="speedRate" 
                :min="0.5" 
                :max="2.0" 
                :step="0.1"
                style="width: 200px; margin-left: 10px;"
              />
            </div>
            
            <div class="voice-type-control">
              <span>发音类型:</span>
              <el-radio-group v-model="voiceType" style="margin-left: 10px;">
                <el-radio label="female">女声</el-radio>
                <el-radio label="male">男声</el-radio>
              </el-radio-group>
            </div>
          </div>
          
          <!-- 翻译输入区 -->
          <div class="translation-input-area">
            <h3>请用中文写出这句话：</h3>
            <el-input
              v-model="userTranslation"
              type="textarea"
              :rows="4"
              placeholder="请输入中文翻译..."
              clearable
            />
            
            <div class="options-container">
              <div v-if="showOptions" class="word-options">
                <el-button
                  v-for="option in translationOptions"
                  :key="option"
                  size="small"
                  @click="selectOption(option)"
                  class="option-button"
                >
                  {{ option }}
                </el-button>
              </div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button @click="showHint" type="warning" :disabled="hintUsed">
              {{ hintUsed ? '已使用提示' : '提示' }}
            </el-button>
            <el-button @click="checkAnswer" type="primary" :disabled="answerChecked">
              检查答案
            </el-button>
            <el-button 
              v-if="answerChecked" 
              @click="nextSentence" 
              type="success"
              :disabled="isLastSentence"
            >
              {{ isLastSentence ? '完成' : '下一句' }}
            </el-button>
          </div>
          
          <!-- 答案反馈 -->
          <div v-if="answerChecked" class="answer-feedback">
            <div v-if="isCorrect" class="correct-answer">
              <el-tag type="success">回答正确！</el-tag>
              <p>你的翻译：{{ userTranslation }}</p>
              <p>参考翻译：{{ sentence.chinese }}</p>
            </div>
            <div v-else class="incorrect-answer">
              <el-tag type="danger">回答有误</el-tag>
              <p>你的翻译：{{ userTranslation }}</p>
              <p>正确翻译：{{ sentence.chinese }}</p>
            </div>
          </div>
          
          <!-- 提示显示 -->
          <div v-if="hintUsed" class="hint-display">
            <el-tag type="info">提示</el-tag>
            <p>{{ hintText }}</p>
          </div>
        </div>
      </div>
      
      <div v-else class="error-state">
        <el-empty description="无法加载句子数据"></el-empty>
        <el-button @click="goBack" type="primary" style="margin-top: 20px;">
          返回课程详情
        </el-button>
      </div>
    </el-main>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getSentenceDetail } from '../services/api';
import type { Sentence } from '../services/api';

const router = useRouter();
const route = useRoute();
const sentence = ref<Sentence | null>(null);
const loading = ref(false);
const userTranslation = ref('');
const answerChecked = ref(false);
const isCorrect = ref(false);
const hintUsed = ref(false);
const hintText = ref('');
const showOptions = ref(true);
const currentAvatarIndex = ref(0);
const progressPercentage = ref(0);
const progressStatus = ref<'success' | 'exception' | 'warning' | ''>('');
const isLastSentence = ref(false);
// 新增：语速和语音类型设置
const speedRate = ref(1.0); // 默认正常速度
const voiceType = ref<'male' | 'female'>('female'); // 默认女声
const availableVoices = ref<SpeechSynthesisVoice[]>([]);

const sentenceId = route.params.sentenceId as string;

// 模拟翻译选项
const translationOptions = ref<string[]>([
  '我', '是', '一名', '中文', '老师', '的', '学生', '喜欢', '学习', '英语'
]);

const loadSentence = async () => {
  loading.value = true;
  try {
    const data = await getSentenceDetail(sentenceId);
    sentence.value = data;
    if (data) {
      // 根据句子内容生成一些相关的选项
      generateOptions(data.chinese);
      // 随机选择头像
      currentAvatarIndex.value = Math.floor(Math.random() * 3);
    }
    
    // 初始化语音列表
    initVoices();
  } catch (error) {
    console.error('Failed to load sentence:', error);
  } finally {
    loading.value = false;
  }
};

// 新增：初始化语音列表
const initVoices = () => {
  // 确保语音列表已加载
  const onVoicesChanged = () => {
    availableVoices.value = speechSynthesis.getVoices();
  };
  
  speechSynthesis.onvoiceschanged = onVoicesChanged;
  // 立即尝试获取一次，因为某些浏览器可能已经加载了语音
  onVoicesChanged();
};

// 新增：根据选择的语音类型获取对应的语音
const getVoiceByType = (type: 'male' | 'female') => {
  // 根据语音名称或语言判断性别
  const voices = availableVoices.value.filter(voice => 
    voice.lang.startsWith('en-') && voice.default
  );
  
  if (voices.length === 0) {
    return null;
  }
  
  // 简单的语音选择逻辑
  // 实际项目中可以根据语音名称更精确地判断性别
  const maleVoices = voices.filter(voice => 
    voice.name.toLowerCase().includes('male') || 
    voice.name.toLowerCase().includes('man') ||
    voice.name.toLowerCase().includes('microsoft david')
  );
  
  const femaleVoices = voices.filter(voice => 
    voice.name.toLowerCase().includes('female') || 
    voice.name.toLowerCase().includes('woman') || 
    voice.name.toLowerCase().includes('microsoft zira')
  );
  
  if (type === 'male' && maleVoices.length > 0) {
    return maleVoices[0];
  } else if (type === 'female' && femaleVoices.length > 0) {
    return femaleVoices[0];
  }
  
  // 如果没有找到匹配的性别语音，返回第一个可用的英语语音
  return voices[0];
};

const generateOptions = (chineseSentence: string) => {
  // 从中文句子中提取一些关键词作为选项
  const words = chineseSentence.split('');
  const uniqueWords = [...new Set(words.filter(w => w.trim() !== ''))];
  
  // 确保选项数量合适
  const tempOptions = [...uniqueWords];
  const additionalOptions = ['的', '是', '我', '你', '他', '她', '老师', '学生', '学习', '喜欢'];
  
  while (tempOptions.length < 8) {
    const randomIndex = Math.floor(Math.random() * additionalOptions.length);
    const randomOption = additionalOptions[randomIndex] || '';
    if (randomOption && !tempOptions.includes(randomOption)) {
      tempOptions.push(randomOption);
    }
  }
  
  // 打乱顺序
  translationOptions.value = tempOptions.sort(() => Math.random() - 0.5);
};

const goBack = () => {
  router.back();
};

const playAudio = () => {
  // 这里可以集成TTS服务来播放句子发音
  console.log('Playing audio for:', sentence.value?.english, 'Speed:', speedRate.value, 'Voice:', voiceType.value);
  // 实际项目中可以使用Web Speech API或其他TTS服务
  const utterance = new SpeechSynthesisUtterance(sentence.value?.english || '');
  utterance.lang = 'en-US';
  
  // 应用语速设置
  utterance.rate = speedRate.value;
  
  // 应用语音类型设置
  const selectedVoice = getVoiceByType(voiceType.value);
  if (selectedVoice) {
    utterance.voice = selectedVoice;
  }
  
  speechSynthesis.speak(utterance);
};

const selectOption = (option: string) => {
  userTranslation.value += option;
};

const showHint = () => {
  if (sentence.value) {
    hintUsed.value = true;
    // 提供首字作为提示
    hintText.value = `这句话的第一个字是："${sentence.value.chinese.charAt(0)}"`;
  }
};

const checkAnswer = () => {
  if (!sentence.value || !userTranslation.value.trim()) {
    return;
  }
  
  answerChecked.value = true;
  // 简单的匹配逻辑，实际应用中可以更复杂
  isCorrect.value = sentence.value.chinese === userTranslation.value.trim();
  
  // 更新进度状态
  progressStatus.value = isCorrect.value ? 'success' : 'exception';
  progressPercentage.value = isCorrect.value ? 100 : 0;
};

const nextSentence = () => {
  // 重置状态
  userTranslation.value = '';
  answerChecked.value = false;
  isCorrect.value = false;
  hintUsed.value = false;
  hintText.value = '';
  progressPercentage.value = 0;
  progressStatus.value = '';
  
  // 实际应用中这里应该导航到下一个句子
  // 由于是模拟环境，我们简单地返回课程详情页
  router.back();
};

onMounted(() => {
  loadSentence();
});
</script>

<style scoped>
.sentence-practice-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #409eff;
  color: white;
  padding: 15px 20px;
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header h1 {
  margin: 0;
  font-size: 24px;
}

.main-content {
  padding: 0 20px 20px;
  flex: 1;
}

.practice-card {
  max-width: 800px;
  margin: 0 auto;
}

.progress-container {
  margin-bottom: 30px;
}

.sentence-display-card {
  margin-bottom: 30px;
  padding: 30px;
  text-align: center;
  position: relative;
}

.sentence-avatar {
  position: absolute;
  left: 30px;
  top: 50%;
  transform: translateY(-50%);
}

.avatar-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
}

.sentence-display {
  margin-left: 80px;
}

.english-sentence {
  font-size: 24px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.sentence-audio {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #606266;
}

.translation-input-area {
  margin-bottom: 20px;
}

.translation-input-area h3 {
  margin-bottom: 15px;
  color: #303133;
}

.options-container {
  margin-top: 15px;
}

.word-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.option-button {
  background-color: #ecf5ff;
  border-color: #d9ecff;
  color: #409eff;
}

.option-button:hover {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 20px;
}

.answer-feedback {
  padding: 20px;
  border-radius: 8px;
  text-align: center;
}

.correct-answer {
  background-color: #f0f9eb;
  border: 1px solid #e1f3d8;
}

.incorrect-answer {
  background-color: #fef0f0;
  border: 1px solid #fde2e2;
}

.answer-feedback p {
  margin: 10px 0 0;
  color: #606266;
}

.hint-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #ecf5ff;
  border-radius: 8px;
  text-align: center;
}

.hint-display p {
  margin: 10px 0 0;
  color: #409eff;
}

.loading, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

/* 新增：语音设置样式 */
.voice-settings {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.speed-control {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #303133;
  font-size: 14px;
}

.voice-type-control {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #303133;
  font-size: 14px;
}

/* 确保滑块在不同浏览器中显示一致 */
.el-slider__runway {
  background-color: #e4e7ed;
}

.el-slider__bar {
  background-color: #409eff;
}

.el-slider__button {
  border-color: #409eff;
}

.el-slider__button:hover {
  border-color: #66b1ff;
}

.el-slider__button.active {
  border-color: #409eff;
  box-shadow: 0 0 0 5px rgba(64, 158, 255, 0.1);
}
</style>