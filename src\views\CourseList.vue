<template>
  <div class="course-list-container">
    <el-header class="header">
      <h1>学生英语学习系统</h1>
    </el-header>
    
    <el-main class="main-content">
      <div class="level-selector">
        <el-radio-group v-model="selectedLevel" size="large" @change="onLevelChange">
          <el-radio-button label="primary-1">小学一年级</el-radio-button>
          <el-radio-button label="primary-2">小学二年级</el-radio-button>
          <el-radio-button label="junior-1">初中一年级</el-radio-button>
          <el-radio-button label="senior-1">高中一年级</el-radio-button>
        </el-radio-group>
      </div>
      
      <div class="course-grid">
        <el-card 
          v-for="course in courses" 
          :key="course.id" 
          class="course-card"
          shadow="hover"
          @click="goToCourseDetail(course.id)"
        >
          <template #header>
            <div class="card-header">
              <span>{{ course.title }}</span>
            </div>
          </template>
          <div class="course-description">
            {{ course.description }}
          </div>
          <div class="course-info">
            <span>{{ course.sentences.length }} 个句子</span>
          </div>
        </el-card>
      </div>
      
      <div v-if="loading" class="loading">
        <el-loading-spinner type="spinner"></el-loading-spinner>
        <span>加载中...</span>
      </div>
      
      <div v-if="!loading && courses.length === 0" class="empty-state">
        <el-empty description="暂无课程数据"></el-empty>
      </div>
    </el-main>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { getCourses } from '../services/api';
import type { Course } from '../services/api';

const router = useRouter();
const selectedLevel = ref('primary-1');
const courses = ref<Course[]>([]);
const loading = ref(false);

const loadCourses = async () => {
  loading.value = true;
  try {
    const data = await getCourses(selectedLevel.value);
    courses.value = data;
  } catch (error) {
    console.error('Failed to load courses:', error);
  } finally {
    loading.value = false;
  }
};

const onLevelChange = () => {
  loadCourses();
};

const goToCourseDetail = (courseId: string) => {
  router.push({
    name: 'CourseDetail',
    params: { level: selectedLevel.value, id: courseId }
  });
};

onMounted(() => {
  loadCourses();
});
</script>

<style scoped>
.course-list-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #409eff;
  color: white;
  text-align: center;
  padding: 20px 0;
  margin-bottom: 20px;
}

.header h1 {
  margin: 0;
  font-size: 28px;
}

.main-content {
  padding: 0 20px 20px;
  flex: 1;
}

.level-selector {
  margin-bottom: 30px;
  text-align: center;
}

.course-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.course-card {
  cursor: pointer;
  transition: transform 0.3s;
}

.course-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.course-description {
  margin-bottom: 10px;
  color: #606266;
}

.course-info {
  margin-top: 10px;
  text-align: right;
  color: #909399;
  font-size: 14px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.empty-state {
  padding: 40px 0;
}
</style>