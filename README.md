# Vue 3 + TypeScript + Vite

# 学生英语学习系统

这是一个为学生设计的英语学习网站，课程内容覆盖从小学一年级到高中的英语句子学习。网站提供了课程选择、课程详情浏览以及句子练习等功能。

## 功能特点

- **多级课程体系**：支持小学一年级至高中的英语学习内容
- **课程列表浏览**：按年级查看和选择不同的英语课程
- **句子练习**：提供英语句子的中文翻译练习功能
- **选项式输入**：通过点击选项单词来组成完整的中文翻译
- **发音播放**：支持英语句子的发音播放
- **即时反馈**：练习后提供正确答案和反馈
- **提示功能**：当遇到困难时可以获取提示

## 技术栈

- **前端**：Vue 3 + TypeScript + Element Plus
- **构建工具**：Vite
- **路由**：Vue Router
- **HTTP客户端**：Axios

## 项目结构

```
src/
  ├── components/       # Vue组件
  ├── views/            # 页面组件
  │   ├── CourseList.vue        # 课程列表页
  │   ├── CourseDetail.vue      # 课程详情页
  │   └── SentencePractice.vue  # 句子练习页
  ├── router/           # 路由配置
  ├── services/         # API服务
  ├── assets/           # 静态资源
  ├── App.vue           # 根组件
  ├── main.ts           # 入口文件
  └── style.css         # 全局样式
```

## 安装和运行

1. 确保已安装Node.js（推荐v16或更高版本）

2. 克隆项目后，安装依赖：

```bash
npm install
```

3. 启动开发服务器：

```bash
npm run dev
```

4. 构建生产版本：

```bash
npm run build
```

5. 预览生产构建：

```bash
npm run preview
```

## 使用说明

1. 打开应用后，首先选择学习的年级（小学一年级至高中一年级）
2. 在课程列表中选择一个感兴趣的课程
3. 浏览课程中的句子列表，点击任意句子开始练习
4. 在练习页面，根据显示的英语句子，使用提供的选项单词组成中文翻译
5. 点击"检查答案"查看是否正确
6. 可以使用"提示"功能获取帮助
7. 完成练习后，可以返回课程详情页继续学习其他句子

## 数据说明

目前系统使用模拟数据进行演示，包含了不同年级的基础英语句子。在实际应用中，可以通过API接口与后端服务集成，获取更多真实的学习内容。

## 开发说明

- 如需添加新的课程内容，请修改`src/services/api.ts`中的模拟数据
- 如需添加新的页面或功能，请在`src/views/`目录下创建新的组件，并在`src/router/index.ts`中配置路由
- 所有样式请遵循现有的CSS规范，保持一致的视觉风格

## License

MIT
