import { createRouter, createWebHistory } from 'vue-router';
import CourseList from '../views/CourseList.vue';
import CourseDetail from '../views/CourseDetail.vue';
import SentencePractice from '../views/SentencePractice.vue';

const routes = [
  {
    path: '/',
    name: 'CourseList',
    component: CourseList
  },
  {
    path: '/course/:level/:id',
    name: 'CourseDetail',
    component: CourseDetail,
    props: true
  },
  {
    path: '/practice/:sentenceId',
    name: 'SentencePractice',
    component: SentencePractice,
    props: true
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

export default router;